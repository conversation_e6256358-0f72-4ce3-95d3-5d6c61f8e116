package com.adrianheras.heartranslated;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.speech.RecognitionListener;
import android.speech.RecognizerIntent;
import android.speech.SpeechRecognizer;
import android.speech.tts.TextToSpeech;
import android.util.Log;
import android.widget.ArrayAdapter;
import android.widget.Spinner;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.google.mlkit.common.model.DownloadConditions;
import com.google.mlkit.nl.translate.TranslateLanguage;
import com.google.mlkit.nl.translate.Translation;
import com.google.mlkit.nl.translate.Translator;
import com.google.mlkit.nl.translate.TranslatorOptions;

import java.util.ArrayList;
import java.util.Locale;

public class MainActivity extends AppCompatActivity implements TextToSpeech.OnInitListener {

    private static final String TAG = "HearTranslated";
    private static final int PERMISSION_REQUEST_RECORD_AUDIO = 1;

    // UI Components
    private Spinner spinnerSourceLanguage;
    private Spinner spinnerTargetLanguage;
    private Switch switchTranslation;
    private Switch switchTTS;
    private TextView textStatus;
    private TextView textTranslationResult;

    // Speech Recognition
    private SpeechRecognizer speechRecognizer;
    private Intent speechRecognizerIntent;

    // Text-to-Speech
    private TextToSpeech textToSpeech;
    private boolean isTTSReady = false;

    // Translation
    private Translator translator;

    // Language arrays
    private String[] sourceLanguages = {"Polaco"};
    private String[] targetLanguages = {"Español"};
    private String[] sourceLanguageCodes = {TranslateLanguage.POLISH};
    private String[] targetLanguageCodes = {TranslateLanguage.SPANISH};

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        initializeViews();
        setupSpinners();
        setupSwitches();
        initializeServices();
        checkPermissions();
    }

    private void initializeViews() {
        spinnerSourceLanguage = findViewById(R.id.spinnerSourceLanguage);
        spinnerTargetLanguage = findViewById(R.id.spinnerTargetLanguage);
        switchTranslation = findViewById(R.id.switchTranslation);
        switchTTS = findViewById(R.id.switchTTS);
        textStatus = findViewById(R.id.textStatus);
        textTranslationResult = findViewById(R.id.textTranslationResult);
    }

    private void setupSpinners() {
        // Source language spinner
        ArrayAdapter<String> sourceAdapter = new ArrayAdapter<>(this,
                android.R.layout.simple_spinner_item, sourceLanguages);
        sourceAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerSourceLanguage.setAdapter(sourceAdapter);

        // Target language spinner
        ArrayAdapter<String> targetAdapter = new ArrayAdapter<>(this,
                android.R.layout.simple_spinner_item, targetLanguages);
        targetAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerTargetLanguage.setAdapter(targetAdapter);
    }

    private void setupSwitches() {
        switchTranslation.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                startTranslation();
            } else {
                stopTranslation();
            }
        });

        switchTTS.setOnCheckedChangeListener((buttonView, isChecked) -> {
            // TTS toggle handled in translation result
        });
    }

    private void initializeServices() {
        Log.d(TAG, "Initializing services");

        // Initialize Text-to-Speech
        textToSpeech = new TextToSpeech(this, this);

        // Initialize Speech Recognizer
        if (SpeechRecognizer.isRecognitionAvailable(this)) {
            Log.d(TAG, "Speech recognition is available");
            speechRecognizer = SpeechRecognizer.createSpeechRecognizer(this);
            speechRecognizer.setRecognitionListener(new SpeechRecognitionListener());

            speechRecognizerIntent = new Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
            speechRecognizerIntent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL,
                    RecognizerIntent.LANGUAGE_MODEL_FREE_FORM);
            speechRecognizerIntent.putExtra(RecognizerIntent.EXTRA_LANGUAGE, "pl-PL"); // Polish
            speechRecognizerIntent.putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true);
            Log.d(TAG, "Speech recognizer configured for Polish language");
        } else {
            Log.e(TAG, "Speech recognition is NOT available on this device");
            Toast.makeText(this, R.string.speech_not_available, Toast.LENGTH_LONG).show();
        }

        // Initialize Translator
        setupTranslator();
    }

    private void setupTranslator() {
        int sourceIndex = spinnerSourceLanguage.getSelectedItemPosition();
        int targetIndex = spinnerTargetLanguage.getSelectedItemPosition();

        TranslatorOptions options = new TranslatorOptions.Builder()
                .setSourceLanguage(sourceLanguageCodes[sourceIndex])
                .setTargetLanguage(targetLanguageCodes[targetIndex])
                .build();

        translator = Translation.getClient(options);

        // Download model if needed
        DownloadConditions conditions = new DownloadConditions.Builder()
                .requireWifi()
                .build();

        translator.downloadModelIfNeeded(conditions)
                .addOnSuccessListener(unused -> {
                    // Model downloaded successfully
                })
                .addOnFailureListener(exception -> {
                    Toast.makeText(this, R.string.translation_error, Toast.LENGTH_SHORT).show();
                });
    }

    private void checkPermissions() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO)
                != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this,
                    new String[]{Manifest.permission.RECORD_AUDIO},
                    PERMISSION_REQUEST_RECORD_AUDIO);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == PERMISSION_REQUEST_RECORD_AUDIO) {
            if (grantResults.length > 0 && grantResults[0] != PackageManager.PERMISSION_GRANTED) {
                Toast.makeText(this, R.string.permission_required, Toast.LENGTH_LONG).show();
                switchTranslation.setChecked(false);
            }
        }
    }

    private void startTranslation() {
        Log.d(TAG, "Starting translation - checking permissions");
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO)
                == PackageManager.PERMISSION_GRANTED) {
            Log.d(TAG, "Audio permission granted");
            if (speechRecognizer != null && speechRecognizerIntent != null) {
                Log.d(TAG, "Starting speech recognition listening");
                textStatus.setText(R.string.listening);
                speechRecognizer.startListening(speechRecognizerIntent);
            } else {
                Log.e(TAG, "Speech recognizer or intent is null");
                Toast.makeText(this, R.string.speech_not_available, Toast.LENGTH_SHORT).show();
                switchTranslation.setChecked(false);
            }
        } else {
            Log.w(TAG, "Audio permission not granted");
            switchTranslation.setChecked(false);
            Toast.makeText(this, R.string.permission_required, Toast.LENGTH_SHORT).show();
        }
    }

    private void stopTranslation() {
        Log.d(TAG, "Stopping translation");
        textStatus.setText("");
        if (speechRecognizer != null) {
            speechRecognizer.stopListening();
            Log.d(TAG, "Speech recognition stopped");
        }
    }

    private void translateText(String text) {
        if (translator != null && !text.trim().isEmpty()) {
            translator.translate(text)
                    .addOnSuccessListener(translatedText -> {
                        textTranslationResult.setText(translatedText);
                        
                        // Speak translated text if TTS is enabled
                        if (switchTTS.isChecked() && isTTSReady) {
                            textToSpeech.speak(translatedText, TextToSpeech.QUEUE_FLUSH, null, null);
                        }
                    })
                    .addOnFailureListener(exception -> {
                        Toast.makeText(this, R.string.translation_error, Toast.LENGTH_SHORT).show();
                    });
        }
    }

    @Override
    public void onInit(int status) {
        if (status == TextToSpeech.SUCCESS) {
            int result = textToSpeech.setLanguage(new Locale("es", "ES")); // Spanish
            if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                Toast.makeText(this, R.string.tts_not_available, Toast.LENGTH_SHORT).show();
                isTTSReady = false;
            } else {
                isTTSReady = true;
            }
        } else {
            Toast.makeText(this, R.string.tts_not_available, Toast.LENGTH_SHORT).show();
            isTTSReady = false;
        }
    }

    private class SpeechRecognitionListener implements RecognitionListener {
        @Override
        public void onReadyForSpeech(Bundle params) {
            Log.d(TAG, "Speech recognizer is ready for speech");
            textStatus.setText(R.string.listening);
        }

        @Override
        public void onBeginningOfSpeech() {
            Log.d(TAG, "Beginning of speech detected!");
            textStatus.setText(R.string.listening);
        }

        @Override
        public void onRmsChanged(float rmsdB) {
            // Log audio level changes to detect microphone activity
            Log.v(TAG, "Audio level (RMS): " + rmsdB + " dB");

            // Log when significant audio is detected
            if (rmsdB > -30) { // Threshold for detecting actual speech/sound
                Log.d(TAG, "AUDIO DETECTED! Level: " + rmsdB + " dB");
            }
        }

        @Override
        public void onBufferReceived(byte[] buffer) {
            Log.v(TAG, "Audio buffer received, size: " + (buffer != null ? buffer.length : 0));
        }

        @Override
        public void onEndOfSpeech() {
            Log.d(TAG, "End of speech detected");
        }

        @Override
        public void onError(int error) {
            String errorMessage = getErrorMessage(error);
            Log.e(TAG, "Speech recognition error: " + errorMessage + " (code: " + error + ")");

            if (switchTranslation.isChecked() && speechRecognizer != null && speechRecognizerIntent != null) {
                Log.d(TAG, "Restarting speech recognition after error");
                // Restart listening if translation is still active
                speechRecognizer.startListening(speechRecognizerIntent);
            }
        }

        @Override
        public void onResults(Bundle results) {
            ArrayList<String> matches = results.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION);
            if (matches != null && !matches.isEmpty()) {
                String spokenText = matches.get(0);
                Log.d(TAG, "Speech recognition result: '" + spokenText + "'");
                translateText(spokenText);
            } else {
                Log.d(TAG, "Speech recognition completed but no results found");
            }

            // Continue listening if translation is still active
            if (switchTranslation.isChecked() && speechRecognizer != null && speechRecognizerIntent != null) {
                Log.d(TAG, "Restarting speech recognition for continuous listening");
                speechRecognizer.startListening(speechRecognizerIntent);
            }
        }

        @Override
        public void onPartialResults(Bundle partialResults) {
            ArrayList<String> matches = partialResults.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION);
            if (matches != null && !matches.isEmpty()) {
                String partialText = matches.get(0);
                Log.d(TAG, "Partial speech result: '" + partialText + "'");
                translateText(partialText);
            }
        }

        @Override
        public void onEvent(int eventType, Bundle params) {
            Log.v(TAG, "Speech recognition event: " + eventType);
        }
    }

    private String getErrorMessage(int error) {
        switch (error) {
            case SpeechRecognizer.ERROR_AUDIO:
                return "Audio recording error";
            case SpeechRecognizer.ERROR_CLIENT:
                return "Client side error";
            case SpeechRecognizer.ERROR_INSUFFICIENT_PERMISSIONS:
                return "Insufficient permissions";
            case SpeechRecognizer.ERROR_NETWORK:
                return "Network error";
            case SpeechRecognizer.ERROR_NETWORK_TIMEOUT:
                return "Network timeout";
            case SpeechRecognizer.ERROR_NO_MATCH:
                return "No match found";
            case SpeechRecognizer.ERROR_RECOGNIZER_BUSY:
                return "Recognition service busy";
            case SpeechRecognizer.ERROR_SERVER:
                return "Server error";
            case SpeechRecognizer.ERROR_SPEECH_TIMEOUT:
                return "No speech input";
            default:
                return "Unknown error";
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (speechRecognizer != null) {
            speechRecognizer.destroy();
        }
        if (textToSpeech != null) {
            textToSpeech.stop();
            textToSpeech.shutdown();
        }
        if (translator != null) {
            translator.close();
        }
    }
}
